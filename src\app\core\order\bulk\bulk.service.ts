import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AbstractControl, ValidationErrors } from '@angular/forms';
import { distinctUntilChanged, Observable, Observer, of, switchMap, timer } from 'rxjs';
import { NotpassReasonComponent } from './notpass-reason/notpass-reason.component';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class BulkService {
  constructor(private http: HttpClient, private modalService: NzModalService, private _translate: TranslateService) {}
  eventEmitter = new EventEmitter();
  isChange = false; // 详情是否有改变
  btnArr: any[] = []; // 按钮权限
  fieldArr: any[] = []; // 字段权限
  lang = this._translate.currentLang;

  translateValue(key: string, param?: any) {
    return this._translate.instant(key, param);
  }

  confirmDialogWithReason() {
    return this.modalService.create({
      nzContent: NotpassReasonComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
    });
  }

  /**
   * 检验编码唯一性
   */
  uniqueValidator = (old_value: null | string = null): any => {
    return (control: AbstractControl) => {
      return timer(500).pipe(
        distinctUntilChanged(),
        switchMap(() => {
          if (control.value === null || control.value === '') {
            return Promise.resolve(null);
          } else {
            return new Observable((observer: Observer<ValidationErrors | null>) => {
              if (control.value !== old_value) {
                this.http.post<any>('/service/order/v1/code/check', { code: control.value }).subscribe((res) => {
                  if (!res.data.exist) {
                    observer.next(null);
                  } else {
                    observer.next({ duplicated: true, msg: res.message });
                  }
                  observer.complete();
                });
              } else {
                observer.next(null);
                observer.complete();
              }
            });
          }
        })
      );
    };
  };
  /**
   * 订单详情成衣进度
   */
  getProdProgress(io_uuid: string): Observable<any> {
    return this.http.get<any>(`/service/order/v1/bi-elan/io-production-progress/${io_uuid}`);
  }
  /**
   * 订单详情二次工艺进度
   */
  getOutData(io_uuid: string): Observable<any> {
    return this.http.get<any>(`/service/order/v1/bi-elan/io-outsource-data/${io_uuid}`);
  }
  // 获取大货订单号
  getBulkOrderCode(id: number): Observable<any> {
    return this.http.get<any>(`/service/procurement-inventory/archive/v1/get-code?code_type=2&brand_id=${id}`);
  }
  // 获取颜色组数据(旧)
  getBasicCascade(): Observable<any> {
    return this.http.get<any>('/service/archive/v1/color/basic_cascade');
  }
  // 获取颜色组数据(新)
  getColorCascade(payload: any): Observable<any> {
    return this.http.post<any>('/service/scm/sample_order/style_lib/color/option', payload);
  }
  // 省市区
  getAddress(): Observable<any> {
    return this.http.get<any>('/service/archive/v1/region?level=0');
  }
  /**
   * 审核通过前保存提交
   */
  saveBulk(payload: any): Observable<any> {
    return this.http.put<any>('/service/order/v1', payload);
  }
  /**
   * 新建订单
   */
  newBulk(payload: any): Observable<any> {
    return this.http.post<any>('/service/order/v1', payload);
  }
  /**
   * 获取大货单号
   */
  getCode(): Observable<any> {
    return this.http.get<any>('/service/order/v1/code');
  }
  /**
   * 审核通过后提交
   */
  editBulk(payload: any): Observable<any> {
    return this.http.put<any>('/service/order/v1/edit', payload);
  }

  /**
   * 订单列表
   */
  bulkList(payload: any): Observable<any> {
    return this.http.post<any>('/service/order/v1/list', payload);
  }

  /**
   * 打印标签码
   */
  getLabelOptions(payload: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/product-label/v1/product-label-card', payload);
  }

  /**
   * 订单下拉
   */
  listOption(payload: any): Observable<any> {
    return this.http.post<any>('/service/order/v1/list_option', payload);
  }
  // 款式分类下拉
  getMaterial(params: any = {}): Observable<any> {
    return this.http.get<any>('/service/order/v1/list_option/material', { params: params });
  }
  /**
   * 获取订单详情
   */
  getDetail(id: number | string, payload: any): Observable<any> {
    return this.http.post<any>(`/service/order/v1/order/${id}`, payload);
  }
  /**
   * 获取跟单员'yuan
   */
  getMerchandiser(data: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/prod_status/v1/prod_status/merchandiser/option', data);
  }

  /**
   * 工厂下拉
   */
  getFactorysIds(): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/outsourcing_management/v1/outsourcing_management/factory-option', {
      column: 'factory_name',
      factory_type: 1,
      limit: 300,
      page: 1,
      value: '',
    });
  }

  // 获取币种下拉
  getUnit(payload: any = {}): Observable<any> {
    return this.http.post<any>('/service/archive/v1/api/unit/basic_option', payload);
  }
  /**
   * 退回修改订单
   */
  auditReturn(payload: any): Observable<any> {
    return this.http.post<any>('/service/order/v1/audit_return', payload);
  }
  /**
   * 订单审核通过
   */
  auditPass(id: number): Observable<any> {
    return this.http.post<any>(`/service/order/v1/audit_pass/${id}`, {});
  }
  /**
   * 批量审核通过
   * @param id // 订单id
   */
  batchPassList(payload: { ids: string[] }) {
    return this.http.post<any>('/service/order/v1/audit_pass_batch', payload);
  }
  /**
   * 取消订单
   */
  cancelOrder(id: number): Observable<any> {
    return this.http.post<any>('/service/order/v1/order/cancel', { id: id, cache: true, production_type: 3 });
  }
  /** 删除订单 */
  deleteOrder(id: any): Observable<any> {
    return this.http.delete<any>(`/service/order/v1/order/${id}`);
  }
  /**
   * 批量退回修改订单
   * @param id // 订单id
   */
  batchModifyList(payload: { ids: string[]; reason: string }) {
    return this.http.post<any>('/service/order/v1/audit_return_batch', payload);
  }
  /**
   * ERP同步（积水潭）
   * @param id // 订单id
   */
  jstSync() {
    return this.http.post<any>('/service/procurement-inventory/v1/bulk-order/jushuitan/sync', {});
  }
  /**
   * 获取所有二次工艺
   */
  extraOption(): Observable<any> {
    return this.http.get<any>('/service/order/v1/extra_process');
  }
  /**
   * 获取所有品牌
   */
  getBrandOption(): Observable<any> {
    return this.http.post<any>('/service/archive/v1/brand/basic_option', { column: 'name', value: '', page: 1, limit: 300 });
  }
  /**
   * 款式分类下拉框数据
   */
  getCascade(): Observable<any> {
    return this.http.get<any>('/service/archive/v1/style/basic_cascade');
  }
  /**
   * 尺码组下拉框数据(旧)
   */
  getSizeOption(payload: any): Observable<any> {
    return this.http.post<any>('/service/archive/v1/size/basic_option', payload);
  }
  /**
   * 尺码组下拉框数据(新)
   */
  getStyleLibSizeOption(payload: any): Observable<any> {
    return this.http.post<any>('/service/scm/sample_order/style_lib/size_group/option', payload);
  }
  /**
   * 尺码下拉框数据(新)
   */
  getStyleLibSizeOnlyOption(payload: any): Observable<any> {
    return this.http.post<any>('/service/scm/sample_order/style_lib/size/option', payload);
  }
  /**
   * 款式编码下拉/联想
   */
  getStyleLib(payload: any): Observable<any> {
    // return this.http.post<any>('/service/scm/style_lib/style_lib_option', payload);
    return this.http.post<any>('/service/procurement-inventory/product-archive/v1/common/list-option', payload);
  }
  /**
   * 款式分类下拉框数据
   */
  getStyleCodeDetail(code: string): Observable<any> {
    const payLoad = encodeURIComponent(code);
    // return this.http.get<any>(`/service/scm/style_lib?style_code=${payLoad}`);
    return this.http.get<any>(`/service/procurement-inventory/product-archive/v1/common/list-detail?style_code=${payLoad}`);
  }
  /**
   * 款式小颜色尺码数据
   */
  getStyleCodeColorSize(code: string): Observable<any> {
    return this.http.get<any>(`/service/order/v1/style-color-size-list?style_code=${code}`);
  }
  /**
   * 处理is_leaf为isLeaf
   */
  onTransOption(value: any) {
    if (value.length) {
      value.forEach((node: any) => {
        node['isLeaf'] = node.is_leaf;
        if (node?.children && node?.children?.length && !node?.is_leaf) {
          this.onTransOption(node?.children);
        }
      });
      return value;
    }
  }

  getDict(payload: any) {
    return this.http.post<any>('/service/scm/dict_category/dict_option', {
      value: '',
      limit: 999999,
      page: 1,
      ...payload,
    });
  }

  // 获取待接单数量
  isAccept = false; // 是否有接单
  getAcceptNumber(): Observable<any> {
    return this.http.get('/service/order/v1/accept/number');
  }

  // 待接单列表
  postAcceptList(payload: any): Observable<any> {
    return this.http.post('/service/order/v1/accept/list', payload);
  }

  // 接单
  postAccept(payload: any): Observable<any> {
    return this.http.post('/service/order/v1/accept/order', payload);
  }

  // 接单详情
  acceptDetail(history_id: string): Observable<any> {
    return this.http.post('/service/order/v1/history/detail', { id: history_id });
  }

  // 款式分类
  getStyle(): Observable<any> {
    return this.http.get('/service/order/v1/accept/list_option/material');
  }

  // 加工厂、跟单员
  getFactoryAndMerchandiser(): Observable<any> {
    return this.http.post('/service/order/v1/option', {});
  }

  // 下载导入模版
  downloadTemplate(): Observable<any> {
    return this.http.post('/service/procurement-inventory/v1/bulk-order/order-import-template/download', {});
  }

  // 上传导入文件
  uploadTemplate(payload: any): Observable<any> {
    return this.http.post('/service/procurement-inventory/v1/bulk-order/order-import', payload);
  }

  // 根据order_ids获取订单信息
  getOrderInfoByOrderIds(payload: any): Observable<any> {
    return this.http.post('/service/procurement-inventory/v1/bulk-order/order-info', payload);
  }

  // 批量上传图片
  batchImportOrderPicture(payload: any): Observable<any> {
    return this.http.post('/service/procurement-inventory/v1/bulk-order/batch-import-order-picture', payload);
  }

  // 去询价
  orderInquiry(payload: any): Observable<any> {
    return this.http.post('/service/procurement-inventory/v1/bulk-order/order-inquiry', payload);
  }
  // 获取尺码组下拉
  getSizeGroup(payload: any) {
    return this.http.post('/service/scm/sample_order/style_lib/size_group/option', {
      limit: 999999,
      page: 1,
      column: 'size_group_name',
      value: '',
      style_lib_id: null,
      style_uuid: null,
      ...payload,
    });
  }

  // 获取尺码下拉
  getSizeOptions(payload: any) {
    return this.http.post('/service/scm/sample_order/style_lib/size/option', {
      column: 'size_name',
      limit: 999999,
      page: 1,
      value: '',
      size_group_id: null,
      style_lib_id: null,
      style_uuid: null,
      ...payload,
    });
  }

  // 订单需求默认图片
  getDefaultStylePicture(payload: any) {
    return this.http.post('/service/procurement-inventory/v1/bulk-order/default-picture', payload);
  }

  // 获取品类下拉
  getCategoryType(payload: any) {
    return this.http.post('/service/archive/v1/style/option', {
      value: '',
      limit: 999999,
      page: 1,
      ...payload,
    });
  }

  // 导入结果
  getImportResult(payload: any) {
    return this.http.post('/service/procurement-inventory/v1/bulk-order/order-import', payload);
  }

  /**
   * 客户下拉
   */
  getCustomerOption(payload: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/sales-contract/v1/customer-option', {
      value: '',
      limit: 999999,
      page: 1,
      ...payload,
    });
  }

  /**
   * 创建生产进度报表
   * @param payload
   */
  createProductionTable(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/prod_status/v1/prod_status/create', payload);
  }

  // 批量创建生产进度报表
  batchCreateProductionTable(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/prod_status/v1/prod_status/create/batch', payload);
  }

  // 获取当前用户基本信息
  getUserBaseInfo() {
    return this.http.get<any>('/service/procurement-inventory/common/v1/base-info');
  }

  getDepartment(): Observable<any> {
    return this.http.get('/service/archive/v1/api/organization/basic_option');
  }

  // 批量更新订单生产周期
  batchUpdateLeadtime(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/v1/bulk-order/batch-update-lead-time', payload);
  }

  // 获取订单需求关联的大货订单列表
  garmentsList(io_code: string) {
    return this.http.post<any>('/service/order/v1/garments/list', {
      page: 1,
      limit: 20,
      order_by: [],
      cache: true,
      where: [
        {
          column: 'io_code',
          op: '=',
          value: io_code,
        },
      ],
      send_status: 0,
    });
  }
  // 批量更新FOB价格
  batchUpdateFobPrice(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/v1/bulk-order/batch-update-fob-price', payload);
  }

  // 转收款单
  toReceipt(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/settlement/v1/receipt-order/to-receipt', payload);
  }

  // 支付额度校验
  validatePaymentQuota(payload: { customer_id: string; currency_id: string }) {
    return this.http.post<{ data: { valid: boolean; err?: string } }>(
      '/service/procurement-inventory/archive/v1/customer/valid-payment-quota',
      payload
    );
  }
}
